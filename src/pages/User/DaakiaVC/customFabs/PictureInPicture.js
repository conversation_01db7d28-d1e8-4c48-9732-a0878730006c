import { useRef, useState, useEffect, useCallback, useMemo } from 'react';
import { createPortal } from 'react-dom';
import { Track } from 'livekit-client';

// Custom PiP Tile Component - responsive and simple
function CustomPipTile({ trackRef }) {
  const videoRef = useRef(null);
  const [hasVideo, setHasVideo] = useState(false);
  const [videoFit, setVideoFit] = useState('cover'); // 'cover' fills space, 'contain' shows full video

  // Handle video track
  useEffect(() => {
    console.log('PiP trackRef changed:', trackRef);

    if (!trackRef?.publication?.track) {
      console.log('No track available');
      setHasVideo(false);
      return;
    }

    const { track, isMuted, isSubscribed } = trackRef.publication;
    const isVideoTrack = track.kind === 'video';
    const isNotMuted = !isMuted;

    console.log('Track details:', {
      kind: track.kind,
      isMuted,
      isSubscribed,
      source: trackRef.publication.source
    });

    if (isVideoTrack && isNotMuted && isSubscribed) {
      // Set hasVideo to true first, then set up video when ref is available
      setHasVideo(true);
    } else {
      console.log('Not showing video:', { isVideoTrack, isNotMuted, isSubscribed });
      setHasVideo(false);
    }
  }, [trackRef]);

  // Separate effect to handle video element setup
  useEffect(() => {
    if (!hasVideo || !videoRef.current || !trackRef?.publication?.track) {
      return;
    }

    const { track } = trackRef.publication;

    try {
      const stream = new MediaStream([track.mediaStreamTrack]);
      videoRef.current.srcObject = stream;
      videoRef.current.play().then(() => {
        console.log('Video playing successfully');
      }).catch(e => {
        console.log('Video play error:', e);
        setHasVideo(false);
      });
    } catch (error) {
      console.log('Video setup error:', error);
      setHasVideo(false);
    }
  }, [hasVideo, trackRef]);

  // Get participant info
  const participant = trackRef?.participant;
  const participantName = participant?.name || participant?.identity || 'You';
  const avatarText = participantName.charAt(0).toUpperCase();

  // Calculate minimal padding for frame only
  const framePadding = 8; // Fixed small padding
  const frameThickness = 3; // Frame border thickness

  return (
    <div style={{
      width: '100%',
      height: '100%',
      padding: `${framePadding}px`,
      boxSizing: 'border-box',
      background: '#000'
    }}>
      {hasVideo ? (
        // Video content with thin frame border - fills most of the space
        <div style={{
          width: '100%',
          height: '100%',
          border: `${frameThickness}px solid #2196F3`, // Blue frame border
          borderRadius: '8px',
          backgroundColor: '#000', // Frame background
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)', // Subtle shadow
          overflow: 'hidden' // Ensure video doesn't overflow frame
        }}>
          <video
            ref={videoRef}
            onClick={() => setVideoFit(prev => prev === 'cover' ? 'contain' : 'cover')}
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'cover', // Always fill the space like Google Meet
              backgroundColor: '#000',
              cursor: 'pointer',
              display: 'block' // Remove any default spacing
            }}
            autoPlay
            playsInline
            muted
            title={`Click to switch to ${videoFit === 'cover' ? 'fit' : 'fill'} mode`}
          />
        </div>
      ) : (
        // Avatar fallback with frame - fills most of the space
        <div style={{
          width: '100%',
          height: '100%',
          border: `${frameThickness}px solid #2196F3`, // Blue frame border
          borderRadius: '8px',
          backgroundColor: '#1a1a1a', // Frame background
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)', // Subtle shadow
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          <div style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'white',
            textAlign: 'center',
            width: '100%',
            height: '100%',
            padding: '16px'
          }}>
            <div style={{
              width: '60px',
              height: '60px',
              borderRadius: '50%',
              background: '#2196F3',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '24px',
              fontWeight: 'bold',
              marginBottom: '12px',
              border: '2px solid #fff' // White border around avatar
            }}>
              {avatarText}
            </div>
            <div style={{
              fontSize: '14px',
              maxWidth: '90%',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap'
            }}>
              {participantName}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export function usePictureInPicture({
  room,
  tracks,
  isTrackReference,
  setIsPIPEnabled,
  setToastNotification,
  setToastStatus,
  setShowToast
}) {
  const pipWindowRef = useRef(null);
  const pipContainerRef = useRef(null);
  const [pipWindowDocument, setPipWindowDocument] = useState(null);

  // Simple configuration
  const defaultConfig = useMemo(() => ({
    width: 320,
    height: 240
  }), []);

  // Check Document PiP support
  const isSupported = useMemo(() => {
    return 'documentPictureInPicture' in window;
  }, []);

  // Get current track to display - screen share first, then camera
  const currentTrack = useMemo(() => {
    console.log('PiP: Evaluating tracks:', tracks?.length);

    if (!tracks?.length) {
      console.log('PiP: No tracks available');
      return null;
    }

    // Priority 1: Screen share (any participant)
    const screenShareTracks = tracks
      .filter(isTrackReference)
      .filter((track) => track.publication.source === Track.Source.ScreenShare);

    console.log('PiP: Screen share tracks found:', screenShareTracks.length);

    if (screenShareTracks.length > 0 && screenShareTracks[0].publication.isSubscribed) {
      console.log('PiP: Using screen share track');
      return screenShareTracks[0];
    }

    // Priority 2: Local camera
    const localCameraTracks = tracks
      .filter(isTrackReference)
      .filter((track) =>
        track.publication.source === Track.Source.Camera &&
        track.participant.isLocal
      );

    console.log('PiP: Local camera tracks found:', localCameraTracks.length);

    if (localCameraTracks.length > 0) {
      console.log('PiP: Using local camera track:', {
        isMuted: localCameraTracks[0].publication.isMuted,
        isSubscribed: localCameraTracks[0].publication.isSubscribed,
        hasTrack: !!localCameraTracks[0].publication.track
      });
      return localCameraTracks[0];
    }

    // No valid tracks found
    console.log('PiP: No valid tracks found');
    return null;
  }, [tracks, isTrackReference, room]);

  // Custom PiP Content using responsive tile
  const PipContent = useCallback(() => {
    if (!currentTrack) {
      // Fallback when no tracks available - create a mock track for local participant
      const mockTrack = {
        participant: room?.localParticipant,
        publication: null
      };
      return <CustomPipTile trackRef={mockTrack} />;
    }

    return <CustomPipTile trackRef={currentTrack} />;
  }, [currentTrack, room]);

  // Close PiP window
  const closePipWindow = useCallback(() => {
    if (pipWindowRef.current) {
      pipWindowRef.current.close();
      pipWindowRef.current = null;
    }
    setPipWindowDocument(null);
    pipContainerRef.current = null;
    setIsPIPEnabled(false);
  }, [setIsPIPEnabled]);

  // Simple styles
  const getPipStyles = useCallback(() => `
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      background: #000;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      overflow: hidden;
      width: 100vw;
      height: 100vh;
      color: white;
    }

    .pip-container {
      width: 100%;
      height: 100%;
      background: #000;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  `, []);

  // Simple error handling
  const handlePipError = useCallback((error) => {
    console.error('PiP error:', error);
    setToastNotification("Failed to open Picture-in-Picture");
    setToastStatus("error");
    setShowToast(true);
  }, [setToastNotification, setToastStatus, setShowToast]);

  // Simple PiP window opening
  const openPipWindow = useCallback(async () => {
    if (!isSupported) {
      handlePipError(new Error('Document Picture-in-Picture not supported'));
      return false;
    }

    if (pipWindowRef.current) {
      return true;
    }

    try {
      const pipWindow = await window.documentPictureInPicture.requestWindow({
        width: defaultConfig.width,
        height: defaultConfig.height,
      });

      pipWindowRef.current = pipWindow;
      setPipWindowDocument(pipWindow.document);
      setIsPIPEnabled(true);

      // Setup document
      const pipDoc = pipWindow.document;
      const style = pipDoc.createElement('style');
      style.textContent = getPipStyles();
      pipDoc.head.appendChild(style);

      const container = pipDoc.createElement('div');
      container.id = 'pip-root';
      pipDoc.body.appendChild(container);
      pipContainerRef.current = container;

      // Simple close handler
      pipWindow.addEventListener('pagehide', () => {
        closePipWindow();
      });

      return true;
    } catch (error) {
      handlePipError(error);
      return false;
    }
  }, [isSupported, defaultConfig, getPipStyles, setIsPIPEnabled, closePipWindow, handlePipError]);

  // Toggle PiP mode
  const togglePipMode = useCallback(async (enabled) => {
    if (enabled) {
      return openPipWindow();
    } else {
      closePipWindow();
      return true;
    }
  }, [openPipWindow, closePipWindow]);

  // Simple PiP content rendering
  const pipPortal = useMemo(() => {
    if (!pipWindowDocument || !pipContainerRef.current) return null;

    return createPortal(
      <div className="pip-container">
        <PipContent />
      </div>,
      pipContainerRef.current
    );
  }, [pipWindowDocument, PipContent]);

  return {
    togglePipMode,
    pipPortal,
    isSupported
  };
}