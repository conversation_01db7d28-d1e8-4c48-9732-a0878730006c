import { useRef, useState, useEffect, useCallback, useMemo } from 'react';
import { createPortal } from 'react-dom';
import { Track } from 'livekit-client';

// Custom PiP Tile Component - responsive and simple
function CustomPipTile({ trackRef, pipWindow }) {
  const videoRef = useRef(null);
  const [windowSize, setWindowSize] = useState({ width: 320, height: 240 });
  const [hasVideo, setHasVideo] = useState(false);

  // Monitor PiP window size changes
  useEffect(() => {
    if (!pipWindow) return;

    const updateSize = () => {
      setWindowSize({
        width: pipWindow.innerWidth,
        height: pipWindow.innerHeight
      });
    };

    // Initial size
    updateSize();

    // Listen for resize events
    pipWindow.addEventListener('resize', updateSize);

    return () => {
      pipWindow.removeEventListener('resize', updateSize);
    };
  }, [pipWindow]);

  // Handle video track
  useEffect(() => {
    if (!trackRef?.publication?.track || !videoRef.current) {
      setHasVideo(false);
      return;
    }

    const track = trackRef.publication.track;
    if (track.kind === 'video' && !trackRef.publication.isMuted) {
      try {
        const stream = new MediaStream([track.mediaStreamTrack]);
        videoRef.current.srcObject = stream;
        videoRef.current.play().then(() => {
          setHasVideo(true);
        }).catch(e => {
          console.log('Video play error:', e);
          setHasVideo(false);
        });
      } catch (error) {
        console.log('Video setup error:', error);
        setHasVideo(false);
      }
    } else {
      setHasVideo(false);
    }
  }, [trackRef]);

  // Get participant info
  const participant = trackRef?.participant;
  const participantName = participant?.name || participant?.identity || 'You';
  const avatarText = participantName.charAt(0).toUpperCase();

  // Calculate responsive sizes with padding
  const padding = Math.max(8, Math.min(windowSize.width, windowSize.height) * 0.05);
  const contentWidth = windowSize.width - (padding * 2);
  const contentHeight = windowSize.height - (padding * 2);

  return (
    <div style={{
      width: '100%',
      height: '100%',
      padding: `${padding}px`,
      boxSizing: 'border-box',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      background: '#000'
    }}>
      {hasVideo ? (
        // Video content
        <video
          ref={videoRef}
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'contain',
            borderRadius: '8px'
          }}
          autoPlay
          playsInline
          muted
        />
      ) : (
        // Avatar fallback
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          color: 'white',
          textAlign: 'center'
        }}>
          <div style={{
            width: Math.min(contentWidth * 0.4, contentHeight * 0.4, 80),
            height: Math.min(contentWidth * 0.4, contentHeight * 0.4, 80),
            borderRadius: '50%',
            background: '#2196F3',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: Math.min(contentWidth * 0.15, contentHeight * 0.15, 32),
            fontWeight: 'bold',
            marginBottom: Math.min(contentHeight * 0.1, 12)
          }}>
            {avatarText}
          </div>
          <div style={{
            fontSize: Math.min(contentWidth * 0.08, contentHeight * 0.08, 14),
            maxWidth: contentWidth * 0.8,
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap'
          }}>
            {participantName}
          </div>
        </div>
      )}
    </div>
  );
}

export function usePictureInPicture({
  room,
  tracks,
  isTrackReference,
  setIsPIPEnabled,
  setToastNotification,
  setToastStatus,
  setShowToast
}) {
  const pipWindowRef = useRef(null);
  const pipContainerRef = useRef(null);
  const [pipWindowDocument, setPipWindowDocument] = useState(null);

  // Simple configuration
  const defaultConfig = useMemo(() => ({
    width: 220,
    height: 120
  }), []);

  // Check Document PiP support
  const isSupported = useMemo(() => {
    return 'documentPictureInPicture' in window;
  }, []);

  // Get current track to display - screen share first, then camera
  const currentTrack = useMemo(() => {
    if (!tracks?.length) return null;

    // Priority 1: Screen share (any participant)
    const screenShareTracks = tracks
      .filter(isTrackReference)
      .filter((track) => track.publication.source === Track.Source.ScreenShare);

    if (screenShareTracks.length > 0 && screenShareTracks[0].publication.isSubscribed) {
      return screenShareTracks[0];
    }

    // Priority 2: Local camera
    const localCameraTracks = tracks
      .filter(isTrackReference)
      .filter((track) =>
        track.publication.source === Track.Source.Camera &&
        track.participant.isLocal
      );

    if (localCameraTracks.length > 0) {
      return localCameraTracks[0];
    }

    // No valid tracks found
    return null;
  }, [tracks, isTrackReference, room]);

  // Custom PiP Content using responsive tile
  const PipContent = useCallback(() => {
    if (!currentTrack) {
      // Fallback when no tracks available - create a mock track for local participant
      const mockTrack = {
        participant: room?.localParticipant,
        publication: null
      };
      return <CustomPipTile trackRef={mockTrack} pipWindow={pipWindowRef.current} />;
    }

    return <CustomPipTile trackRef={currentTrack} pipWindow={pipWindowRef.current} />;
  }, [currentTrack, room]);

  // Close PiP window
  const closePipWindow = useCallback(() => {
    if (pipWindowRef.current) {
      pipWindowRef.current.close();
      pipWindowRef.current = null;
    }
    setPipWindowDocument(null);
    pipContainerRef.current = null;
    setIsPIPEnabled(false);
  }, [setIsPIPEnabled]);

  // Simple styles
  const getPipStyles = useCallback(() => `
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      background: #000;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      overflow: hidden;
      width: 100vw;
      height: 100vh;
      color: white;
    }

    .pip-container {
      width: 100%;
      height: 100%;
      background: #000;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  `, []);

  // Simple error handling
  const handlePipError = useCallback((error) => {
    console.error('PiP error:', error);
    setToastNotification("Failed to open Picture-in-Picture");
    setToastStatus("error");
    setShowToast(true);
  }, [setToastNotification, setToastStatus, setShowToast]);

  // Simple PiP window opening
  const openPipWindow = useCallback(async () => {
    if (!isSupported) {
      handlePipError(new Error('Document Picture-in-Picture not supported'));
      return false;
    }

    if (pipWindowRef.current) {
      return true;
    }

    try {
      const pipWindow = await window.documentPictureInPicture.requestWindow({
        width: defaultConfig.width,
        height: defaultConfig.height,
      });

      pipWindowRef.current = pipWindow;
      setPipWindowDocument(pipWindow.document);
      setIsPIPEnabled(true);

      // Setup document
      const pipDoc = pipWindow.document;
      const style = pipDoc.createElement('style');
      style.textContent = getPipStyles();
      pipDoc.head.appendChild(style);

      const container = pipDoc.createElement('div');
      container.id = 'pip-root';
      pipDoc.body.appendChild(container);
      pipContainerRef.current = container;

      // Simple close handler
      pipWindow.addEventListener('pagehide', () => {
        closePipWindow();
      });

      return true;
    } catch (error) {
      handlePipError(error);
      return false;
    }
  }, [isSupported, defaultConfig, getPipStyles, setIsPIPEnabled, closePipWindow, handlePipError]);

  // Toggle PiP mode
  const togglePipMode = useCallback(async (enabled) => {
    if (enabled) {
      return openPipWindow();
    } else {
      closePipWindow();
      return true;
    }
  }, [openPipWindow, closePipWindow]);

  // Simple PiP content rendering
  const pipPortal = useMemo(() => {
    if (!pipWindowDocument || !pipContainerRef.current) return null;

    return createPortal(
      <div className="pip-container">
        <PipContent />
      </div>,
      pipContainerRef.current
    );
  }, [pipWindowDocument, PipContent]);

  return {
    togglePipMode,
    pipPortal,
    isSupported
  };
}